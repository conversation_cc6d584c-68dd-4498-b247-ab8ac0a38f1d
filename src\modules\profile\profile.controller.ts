import { Request, Response } from 'express';
import { profileService } from '../../services/profile.service';
import { PERMISSIONS } from '../../config/permissions';

/**
 * Profile Controller - Handles HTTP requests for profile operations
 * Implements Single Responsibility Principle by handling only profile HTTP operations
 */
export class ProfileController {

  /**
   * Get current user's profile
   * @param req - Express request object
   * @param res - Express response object
   */
  async getMyProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
        return;
      }

      const profile = await profileService.getProfileByUserId(req.user.userId);
      
      // Remove sensitive information
      const { password, ...userWithoutPassword } = profile;

      res.json({
        message: 'Profile retrieved successfully',
        profile: userWithoutPassword,
      });
    } catch (error) {
      console.error('Error fetching own profile:', error);
      
      if (error instanceof Error && error.message === 'User not found') {
        res.status(404).json({ 
          error: 'Profile not found',
          code: 'PROFILE_NOT_FOUND'
        });
        return;
      }

      res.status(500).json({ 
        error: 'Failed to fetch profile',
        code: 'PROFILE_FETCH_ERROR'
      });
    }
  }

  /**
   * Get any user's profile (admin/reception only)
   * @param req - Express request object
   * @param res - Express response object
   */
  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId || isNaN(parseInt(userId))) {
        res.status(400).json({ 
          error: 'Valid user ID is required',
          code: 'INVALID_USER_ID'
        });
        return;
      }

      const profile = await profileService.getProfileByUserId(parseInt(userId));
      
      // Remove sensitive information
      const { password, ...userWithoutPassword } = profile;

      res.json({
        message: 'Profile retrieved successfully',
        profile: userWithoutPassword,
      });
    } catch (error) {
      console.error('Error fetching profile:', error);
      
      if (error instanceof Error && error.message === 'User not found') {
        res.status(404).json({ 
          error: 'Profile not found',
          code: 'PROFILE_NOT_FOUND'
        });
        return;
      }

      res.status(500).json({ 
        error: 'Failed to fetch profile',
        code: 'PROFILE_FETCH_ERROR'
      });
    }
  }

  /**
   * Get all profiles with pagination and filtering
   * @param req - Express request object
   * @param res - Express response object
   */
  async getAllProfiles(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = '1',
        limit = '10',
        role,
        search,
      } = req.query;

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      if (isNaN(pageNum) || pageNum < 1) {
        res.status(400).json({ 
          error: 'Invalid page number',
          code: 'INVALID_PAGE'
        });
        return;
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        res.status(400).json({ 
          error: 'Invalid limit (must be between 1 and 100)',
          code: 'INVALID_LIMIT'
        });
        return;
      }

      const result = await profileService.getAllProfiles({
        page: pageNum,
        limit: limitNum,
        role: role as any,
        search: search as string,
      });

      // Remove passwords from all users
      const sanitizedUsers = result.users.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      res.json({
        message: 'Profiles retrieved successfully',
        users: sanitizedUsers,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error fetching all profiles:', error);
      res.status(500).json({ 
        error: 'Failed to fetch profiles',
        code: 'PROFILES_FETCH_ERROR'
      });
    }
  }

  /**
   * Update current user's profile
   * @param req - Express request object
   * @param res - Express response object
   */
  async updateMyProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
        return;
      }

      if (!req.user.profileType) {
        res.status(400).json({ 
          error: 'Profile type not found',
          code: 'PROFILE_TYPE_MISSING'
        });
        return;
      }

      // Validate that user is not trying to update sensitive fields
      const restrictedFields = ['userId', 'id', 'customerId'];
      const hasRestrictedFields = restrictedFields.some(field => req.body.hasOwnProperty(field));
      
      if (hasRestrictedFields) {
        res.status(400).json({ 
          error: 'Cannot update restricted fields',
          code: 'RESTRICTED_FIELDS',
          restrictedFields
        });
        return;
      }

      const updatedProfile = await profileService.updateProfile(
        req.user.userId,
        req.user.profileType,
        req.body
      );

      res.json({
        message: 'Profile updated successfully',
        profile: updatedProfile,
      });
    } catch (error) {
      console.error('Error updating own profile:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Unique constraint')) {
          res.status(409).json({ 
            error: 'Profile data conflicts with existing data',
            code: 'PROFILE_CONFLICT'
          });
          return;
        }
        
        if (error.message === 'Failed to update profile') {
          res.status(400).json({ 
            error: 'Invalid profile data',
            code: 'INVALID_PROFILE_DATA'
          });
          return;
        }
      }

      res.status(500).json({ 
        error: 'Failed to update profile',
        code: 'PROFILE_UPDATE_ERROR'
      });
    }
  }

  /**
   * Update any user's profile (admin/reception only)
   * @param req - Express request object
   * @param res - Express response object
   */
  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId || isNaN(parseInt(userId))) {
        res.status(400).json({ 
          error: 'Valid user ID is required',
          code: 'INVALID_USER_ID'
        });
        return;
      }

      // Get user's profile type
      const profileInfo = await profileService.profileExists(parseInt(userId));
      
      if (!profileInfo.exists || !profileInfo.profileType) {
        res.status(404).json({ 
          error: 'Profile not found',
          code: 'PROFILE_NOT_FOUND'
        });
        return;
      }

      // Validate that admin is not trying to update sensitive fields
      const restrictedFields = ['userId', 'id', 'customerId'];
      const hasRestrictedFields = restrictedFields.some(field => req.body.hasOwnProperty(field));
      
      if (hasRestrictedFields) {
        res.status(400).json({ 
          error: 'Cannot update restricted fields',
          code: 'RESTRICTED_FIELDS',
          restrictedFields
        });
        return;
      }

      const updatedProfile = await profileService.updateProfile(
        parseInt(userId),
        profileInfo.profileType,
        req.body
      );

      res.json({
        message: 'Profile updated successfully',
        profile: updatedProfile,
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Unique constraint')) {
          res.status(409).json({ 
            error: 'Profile data conflicts with existing data',
            code: 'PROFILE_CONFLICT'
          });
          return;
        }
        
        if (error.message === 'Failed to update profile') {
          res.status(400).json({ 
            error: 'Invalid profile data',
            code: 'INVALID_PROFILE_DATA'
          });
          return;
        }
      }

      res.status(500).json({ 
        error: 'Failed to update profile',
        code: 'PROFILE_UPDATE_ERROR'
      });
    }
  }

  /**
   * Delete a user profile (admin only)
   * @param req - Express request object
   * @param res - Express response object
   */
  async deleteProfile(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId || isNaN(parseInt(userId))) {
        res.status(400).json({ 
          error: 'Valid user ID is required',
          code: 'INVALID_USER_ID'
        });
        return;
      }

      // Prevent admin from deleting their own account
      if (req.user && req.user.userId === parseInt(userId)) {
        res.status(400).json({ 
          error: 'Cannot delete your own profile',
          code: 'SELF_DELETE_FORBIDDEN'
        });
        return;
      }

      const deletedProfile = await profileService.deleteProfile(parseInt(userId));

      res.json({
        message: 'Profile deleted successfully',
        deletedUser: {
          id: deletedProfile.id,
          email: deletedProfile.email,
          role: deletedProfile.role,
        },
      });
    } catch (error) {
      console.error('Error deleting profile:', error);
      
      if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
        res.status(404).json({ 
          error: 'Profile not found',
          code: 'PROFILE_NOT_FOUND'
        });
        return;
      }

      res.status(500).json({ 
        error: 'Failed to delete profile',
        code: 'PROFILE_DELETE_ERROR'
      });
    }
  }

  /**
   * Check if profile exists and get basic info
   * @param req - Express request object
   * @param res - Express response object
   */
  async checkProfile(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId || isNaN(parseInt(userId))) {
        res.status(400).json({ 
          error: 'Valid user ID is required',
          code: 'INVALID_USER_ID'
        });
        return;
      }

      const profileInfo = await profileService.profileExists(parseInt(userId));

      res.json({
        message: 'Profile check completed',
        ...profileInfo,
      });
    } catch (error) {
      console.error('Error checking profile:', error);
      res.status(500).json({ 
        error: 'Failed to check profile',
        code: 'PROFILE_CHECK_ERROR'
      });
    }
  }
}

// Export singleton instance
export const profileController = new ProfileController();
