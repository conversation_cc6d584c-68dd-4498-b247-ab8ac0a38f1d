// JWT Payload Structure
interface JWTPayload {
  userId: number;
  email: string;
  role: Role;
  profileId?: number;
  profileType?: 'client' | 'staff' | 'mechanic';
}

// Role Permission Mapping
interface RolePermissions {
  [key: string]: string[];
}

// Authentication Request Extensions
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
      permissions?: string[];
    }
  }
}