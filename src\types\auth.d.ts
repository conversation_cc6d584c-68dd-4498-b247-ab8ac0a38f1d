import { Role } from '@prisma/client';

// JWT Payload Structure - Contains essential user information
export interface JWTPayload {
  userId: number;
  email: string;
  role: Role;
  profileId?: number;
  profileType?: 'client' | 'staff' | 'mechanic';
  iat?: number;  // Issued at
  exp?: number;  // Expires at
}

// Authentication Response Structure
export interface AuthResponse {
  message: string;
  token: string;
  refreshToken?: string;
  user: {
    id: number;
    email: string;
    role: Role;
    profileType?: string;
    permissions: string[];
  };
}

// Registration Request Types
export interface ClientRegistrationRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  address: string;
  postCode: string;
  country: string;
  phoneNumber: string;
  birthday: string;
  isCorporate?: boolean;
  companyName?: string;
  emergencyContactPersonName?: string;
  emergencyContactRelationship?: string;
  emergencyContactPhoneNumber?: string;
}

export interface StaffRegistrationRequest {
  email: string;
  password: string;
  role: 'ADMIN' | 'RECEPTION' | 'TEAM_LEADER' | 'ASSISTANCE';
  employeeId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  position?: string;
}

export interface MechanicRegistrationRequest {
  email: string;
  password: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  certificationNumber: string;
  specialization?: string;
}

// Login Request
export interface LoginRequest {
  email: string;
  password: string;
}

// Role Permission Mapping
export interface RolePermissions {
  [key: string]: string[];
}

// Profile Types Union
export type ProfileData = {
  client?: any;
  staff?: any;
  mechanic?: any;
};

// Error Response Structure
export interface AuthError {
  error: string;
  code?: string;
  details?: any;
}

// Authentication Request Extensions
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
      permissions?: string[];
    }
  }
}

export {};