import express from 'express';
import { profile<PERSON>ontroller } from './profile.controller';
import { 
  authenticate,
  requirePermission,
  requireOwnership,
  adminOnly,
  profileRead,
  profileUpdate,
  ownProfileOnly,
  selfOrAdmin
} from '../../config/middleware';
import { PERMISSIONS } from '../../config/permissions';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Profile
 *   description: User profile management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     ClientProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Profile ID
 *         customerId:
 *           type: string
 *           description: Unique customer ID
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         address:
 *           type: string
 *           description: Address
 *         postCode:
 *           type: string
 *           description: Postal code
 *         country:
 *           type: string
 *           description: Country
 *         phoneNumber:
 *           type: string
 *           description: Phone number
 *         birthday:
 *           type: string
 *           format: date
 *           description: Date of birth
 *         isCorporate:
 *           type: boolean
 *           description: Whether this is a corporate account
 *         companyName:
 *           type: string
 *           description: Company name (if corporate)
 *         emergencyContactPersonName:
 *           type: string
 *           description: Emergency contact name
 *         emergencyContactRelationship:
 *           type: string
 *           description: Emergency contact relationship
 *         emergencyContactPhoneNumber:
 *           type: string
 *           description: Emergency contact phone
 *     
 *     StaffProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Profile ID
 *         employeeId:
 *           type: string
 *           description: Employee ID
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         phoneNumber:
 *           type: string
 *           description: Phone number
 *         position:
 *           type: string
 *           description: Job position
 *     
 *     MechanicProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Profile ID
 *         employeeId:
 *           type: string
 *           description: Employee ID
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         phoneNumber:
 *           type: string
 *           description: Phone number
 *         certificationNumber:
 *           type: string
 *           description: Certification number
 *         specialization:
 *           type: string
 *           description: Area of specialization
 */

// ===== PROFILE ROUTES =====

/**
 * @swagger
 * /api/profile/me:
 *   get:
 *     tags: [Profile]
 *     summary: Get current user's profile
 *     description: Retrieve the authenticated user's profile information
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 profile:
 *                   type: object
 *                   description: User profile data
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Profile not found
 *       500:
 *         description: Server error
 */
router.get('/me', 
  authenticate, 
  profileController.getMyProfile.bind(profileController)
);

/**
 * @swagger
 * /api/profile/me:
 *   put:
 *     tags: [Profile]
 *     summary: Update current user's profile
 *     description: Update the authenticated user's profile information
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Profile data to update (varies by profile type)
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Invalid profile data
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       409:
 *         description: Profile data conflicts
 *       500:
 *         description: Server error
 */
router.put('/me', 
  authenticate,
  requirePermission(PERMISSIONS.PROFILE_UPDATE_OWN),
  profileController.updateMyProfile.bind(profileController)
);

/**
 * @swagger
 * /api/profile/{userId}:
 *   get:
 *     tags: [Profile]
 *     summary: Get user profile by ID
 *     description: Retrieve any user's profile (admin/reception only)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Profile not found
 *       500:
 *         description: Server error
 */
router.get('/:userId', 
  authenticate,
  requirePermission(PERMISSIONS.PROFILE_READ_ALL),
  profileController.getProfile.bind(profileController)
);

/**
 * @swagger
 * /api/profile/{userId}:
 *   put:
 *     tags: [Profile]
 *     summary: Update user profile by ID
 *     description: Update any user's profile (admin/reception only)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Profile data to update
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Invalid user ID or profile data
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Profile not found
 *       409:
 *         description: Profile data conflicts
 *       500:
 *         description: Server error
 */
router.put('/:userId', 
  authenticate,
  requirePermission(PERMISSIONS.PROFILE_UPDATE_ALL),
  profileController.updateProfile.bind(profileController)
);

/**
 * @swagger
 * /api/profile/{userId}:
 *   delete:
 *     tags: [Profile]
 *     summary: Delete user profile
 *     description: Delete a user profile and associated account (admin only)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Profile deleted successfully
 *       400:
 *         description: Invalid user ID or cannot delete own profile
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Profile not found
 *       500:
 *         description: Server error
 */
router.delete('/:userId', 
  ...adminOnly,
  profileController.deleteProfile.bind(profileController)
);

/**
 * @swagger
 * /api/profile:
 *   get:
 *     tags: [Profile]
 *     summary: Get all profiles
 *     description: Retrieve all user profiles with pagination and filtering (admin/reception only)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 100
 *         description: Number of profiles per page
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [CLIENT, ADMIN, RECEPTION, MECHANIC, TEAM_LEADER, ASSISTANCE]
 *         description: Filter by user role
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in names, emails, phone numbers, employee IDs
 *     responses:
 *       200:
 *         description: Profiles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *       400:
 *         description: Invalid query parameters
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/', 
  authenticate,
  requirePermission(PERMISSIONS.PROFILE_READ_ALL),
  profileController.getAllProfiles.bind(profileController)
);

/**
 * @swagger
 * /api/profile/check/{userId}:
 *   get:
 *     tags: [Profile]
 *     summary: Check if profile exists
 *     description: Check if a user has a profile and get basic info
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Profile check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 exists:
 *                   type: boolean
 *                 profileType:
 *                   type: string
 *                   enum: [client, staff, mechanic]
 *                 profileId:
 *                   type: integer
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/check/:userId', 
  authenticate,
  requirePermission(PERMISSIONS.USER_READ),
  profileController.checkProfile.bind(profileController)
);

export default router;
