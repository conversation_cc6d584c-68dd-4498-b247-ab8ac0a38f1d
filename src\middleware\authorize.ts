import { Request, Response, NextFunction } from 'express';
import { Role } from '../../generated/prisma';
import { ROLE_PERMISSIONS, hasPermission, hasAnyPermission, hasAllPermissions } from '../config/permissions';

/**
 * Role-based authorization middleware
 * Checks if the authenticated user has one of the required roles
 * @param allowedRoles - Array of roles that are allowed to access the resource
 * @returns Express middleware function
 */
export const requireRole = (...allowedRoles: Role[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check if user has one of the allowed roles
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_ROLE',
        required: allowedRoles,
        current: req.user.role,
        message: `Access denied. Required roles: ${allowedRoles.join(', ')}. Current role: ${req.user.role}`
      });
    }

    next();
  };
};

/**
 * Permission-based authorization middleware
 * Checks if the authenticated user has all the required permissions
 * @param permissions - Array of permissions that are required
 * @returns Express middleware function
 */
export const requirePermission = (...permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Get user permissions
    const userPermissions = ROLE_PERMISSIONS[req.user.role] || [];
    
    // Check if user has all required permissions
    const hasAllRequired = permissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAllRequired) {
      const missingPermissions = permissions.filter(permission => 
        !userPermissions.includes(permission)
      );

      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions,
        missing: missingPermissions,
        available: userPermissions,
        message: `Access denied. Missing permissions: ${missingPermissions.join(', ')}`
      });
    }

    // Attach permissions to request for use in controllers
    req.permissions = userPermissions;
    next();
  };
};

/**
 * Permission-based authorization middleware (requires ANY of the permissions)
 * Checks if the authenticated user has at least one of the required permissions
 * @param permissions - Array of permissions (user needs at least one)
 * @returns Express middleware function
 */
export const requireAnyPermission = (...permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Get user permissions
    const userPermissions = ROLE_PERMISSIONS[req.user.role] || [];
    
    // Check if user has any of the required permissions
    const hasAnyRequired = permissions.some(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAnyRequired) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions,
        available: userPermissions,
        message: `Access denied. Required at least one of: ${permissions.join(', ')}`
      });
    }

    // Attach permissions to request for use in controllers
    req.permissions = userPermissions;
    next();
  };
};

/**
 * Resource ownership validation middleware
 * Checks if the user owns the resource or has admin privileges
 * @param resourceField - Field name in request params/body that contains the user ID
 * @param options - Configuration options
 * @returns Express middleware function
 */
export const requireOwnership = (
  resourceField: string = 'userId',
  options: {
    adminOverride?: boolean;
    paramLocation?: 'params' | 'body' | 'query';
    allowedRoles?: Role[];
  } = {}
) => {
  const { 
    adminOverride = true, 
    paramLocation = 'params',
    allowedRoles = ['ADMIN'] 
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Admin override - admins can access all resources
    if (adminOverride && allowedRoles.includes(req.user.role)) {
      return next();
    }

    // Get resource user ID from specified location
    let resourceUserId: string | number;
    switch (paramLocation) {
      case 'params':
        resourceUserId = req.params[resourceField];
        break;
      case 'body':
        resourceUserId = req.body[resourceField];
        break;
      case 'query':
        resourceUserId = req.query[resourceField] as string;
        break;
      default:
        resourceUserId = req.params[resourceField];
    }

    // Check if resource user ID exists
    if (!resourceUserId) {
      return res.status(400).json({ 
        error: `Missing ${resourceField} in ${paramLocation}`,
        code: 'MISSING_RESOURCE_ID'
      });
    }

    // Convert to number for comparison
    const resourceUserIdNum = parseInt(resourceUserId.toString());
    
    // Check if user owns the resource
    if (resourceUserIdNum !== req.user.userId) {
      return res.status(403).json({ 
        error: 'Access denied: Resource ownership required',
        code: 'OWNERSHIP_REQUIRED',
        message: 'You can only access your own resources'
      });
    }

    next();
  };
};

/**
 * Profile type validation middleware
 * Checks if the user has the required profile type
 * @param allowedProfileTypes - Array of allowed profile types
 * @returns Express middleware function
 */
export const requireProfileType = (...allowedProfileTypes: ('client' | 'staff' | 'mechanic')[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check if user has a profile type
    if (!req.user.profileType) {
      return res.status(403).json({ 
        error: 'Profile required',
        code: 'PROFILE_REQUIRED',
        message: 'User must have a complete profile to access this resource'
      });
    }

    // Check if user has one of the allowed profile types
    if (!allowedProfileTypes.includes(req.user.profileType)) {
      return res.status(403).json({ 
        error: 'Invalid profile type',
        code: 'INVALID_PROFILE_TYPE',
        required: allowedProfileTypes,
        current: req.user.profileType,
        message: `Access denied. Required profile types: ${allowedProfileTypes.join(', ')}. Current: ${req.user.profileType}`
      });
    }

    next();
  };
};

/**
 * Conditional authorization middleware
 * Applies different authorization based on conditions
 * @param condition - Function that returns true/false based on request
 * @param trueMiddleware - Middleware to apply if condition is true
 * @param falseMiddleware - Middleware to apply if condition is false
 * @returns Express middleware function
 */
export const conditionalAuth = (
  condition: (req: Request) => boolean,
  trueMiddleware: (req: Request, res: Response, next: NextFunction) => void,
  falseMiddleware: (req: Request, res: Response, next: NextFunction) => void
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (condition(req)) {
      trueMiddleware(req, res, next);
    } else {
      falseMiddleware(req, res, next);
    }
  };
};

/**
 * Rate limiting by role middleware
 * Different rate limits for different roles
 * @param limits - Object mapping roles to rate limits
 * @returns Express middleware function
 */
export const rateLimitByRole = (limits: { [key in Role]?: number }) => {
  const requestCounts = new Map<string, { count: number; resetTime: number }>();
  const windowMs = 15 * 60 * 1000; // 15 minutes

  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRole = req.user.role;
    const limit = limits[userRole];

    // If no limit specified for this role, allow through
    if (!limit) {
      return next();
    }

    const key = `${req.user.userId}-${userRole}`;
    const now = Date.now();
    const userRequests = requestCounts.get(key);

    // Reset if window has passed
    if (!userRequests || now > userRequests.resetTime) {
      requestCounts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    // Check if limit exceeded
    if (userRequests.count >= limit) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        limit,
        resetTime: userRequests.resetTime,
        message: `Rate limit of ${limit} requests per 15 minutes exceeded for role ${userRole}`
      });
    }

    // Increment count
    userRequests.count++;
    requestCounts.set(key, userRequests);

    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': limit.toString(),
      'X-RateLimit-Remaining': (limit - userRequests.count).toString(),
      'X-RateLimit-Reset': new Date(userRequests.resetTime).toISOString(),
    });

    next();
  };
};
