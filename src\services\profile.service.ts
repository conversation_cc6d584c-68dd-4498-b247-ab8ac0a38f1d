import { prisma } from '../config/db';
import { Role } from '../../generated/prisma';

/**
 * Profile Service - Handles all profile-related operations
 * Implements Single Responsibility Principle by handling only profile operations
 */
export class ProfileService {
  
  /**
   * Create profile based on user role
   * @param userId - User ID to create profile for
   * @param role - User role to determine profile type
   * @param profileData - Profile data specific to the role
   * @returns Created profile
   */
  async createProfile(userId: number, role: Role, profileData: any) {
    try {
      switch (role) {
        case 'CLIENT':
          return await this.createClientProfile(userId, profileData);
        
        case 'MECHANIC':
          return await this.createMechanicProfile(userId, profileData);
        
        case 'ADMIN':
        case 'RECEPTION':
        case 'TEAM_LEADER':
        case 'ASSISTANCE':
          return await this.createStaffProfile(userId, profileData);
        
        default:
          throw new Error(`Unsupported role for profile creation: ${role}`);
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      throw new Error('Failed to create profile');
    }
  }

  /**
   * Create client profile
   * @param userId - User ID
   * @param profileData - Client profile data
   * @returns Created client profile
   */
  private async createClientProfile(userId: number, profileData: any) {
    const {
      firstName,
      lastName,
      address,
      postCode,
      country,
      phoneNumber,
      birthday,
      isCorporate = false,
      companyName,
      emergencyContactPersonName,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
    } = profileData;

    return await prisma.clientProfile.create({
      data: {
        userId,
        firstName,
        lastName,
        address,
        postCode,
        country,
        phoneNumber,
        birthday: new Date(birthday),
        isCorporate,
        ...(companyName && { companyName }),
        ...(emergencyContactPersonName && { emergencyContactPersonName }),
        ...(emergencyContactRelationship && { emergencyContactRelationship }),
        ...(emergencyContactPhoneNumber && { emergencyContactPhoneNumber }),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Create staff profile
   * @param userId - User ID
   * @param profileData - Staff profile data
   * @returns Created staff profile
   */
  private async createStaffProfile(userId: number, profileData: any) {
    const {
      employeeId,
      firstName,
      lastName,
      phoneNumber,
      position,
    } = profileData;

    return await prisma.staffProfile.create({
      data: {
        userId,
        employeeId,
        firstName,
        lastName,
        phoneNumber,
        ...(position && { position }),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Create mechanic profile
   * @param userId - User ID
   * @param profileData - Mechanic profile data
   * @returns Created mechanic profile
   */
  private async createMechanicProfile(userId: number, profileData: any) {
    const {
      employeeId,
      firstName,
      lastName,
      phoneNumber,
      certificationNumber,
      specialization,
    } = profileData;

    return await prisma.mechanicProfile.create({
      data: {
        userId,
        employeeId,
        firstName,
        lastName,
        phoneNumber,
        certificationNumber,
        ...(specialization && { specialization }),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Get profile by user ID with all related data
   * @param userId - User ID
   * @returns User with profile data
   */
  async getProfileByUserId(userId: number) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          clientProfile: {
            include: {
              licenses: true,
            },
          },
          staffProfile: true,
          mechanicProfile: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      return user;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw new Error('Failed to fetch profile');
    }
  }

  /**
   * Get all profiles with pagination and filtering
   * @param options - Query options
   * @returns Paginated profiles
   */
  async getAllProfiles(options: {
    page?: number;
    limit?: number;
    role?: Role;
    search?: string;
  } = {}) {
    try {
      const { page = 1, limit = 10, role, search } = options;
      const skip = (page - 1) * limit;

      const where: any = {};
      
      if (role) {
        where.role = role;
      }

      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          {
            clientProfile: {
              OR: [
                { firstName: { contains: search, mode: 'insensitive' } },
                { lastName: { contains: search, mode: 'insensitive' } },
                { phoneNumber: { contains: search } },
              ],
            },
          },
          {
            staffProfile: {
              OR: [
                { firstName: { contains: search, mode: 'insensitive' } },
                { lastName: { contains: search, mode: 'insensitive' } },
                { employeeId: { contains: search } },
              ],
            },
          },
          {
            mechanicProfile: {
              OR: [
                { firstName: { contains: search, mode: 'insensitive' } },
                { lastName: { contains: search, mode: 'insensitive' } },
                { employeeId: { contains: search } },
              ],
            },
          },
        ];
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          include: {
            clientProfile: true,
            staffProfile: true,
            mechanicProfile: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        }),
        prisma.user.count({ where }),
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching all profiles:', error);
      throw new Error('Failed to fetch profiles');
    }
  }

  /**
   * Update profile based on profile type
   * @param userId - User ID
   * @param profileType - Type of profile to update
   * @param updateData - Data to update
   * @returns Updated profile
   */
  async updateProfile(userId: number, profileType: string, updateData: any) {
    try {
      switch (profileType) {
        case 'client':
          return await this.updateClientProfile(userId, updateData);
        
        case 'staff':
          return await this.updateStaffProfile(userId, updateData);
        
        case 'mechanic':
          return await this.updateMechanicProfile(userId, updateData);
        
        default:
          throw new Error(`Unsupported profile type: ${profileType}`);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  /**
   * Update client profile
   * @param userId - User ID
   * @param updateData - Data to update
   * @returns Updated client profile
   */
  private async updateClientProfile(userId: number, updateData: any) {
    // Remove undefined values and prepare update data
    const cleanData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Handle birthday conversion if present
    if (cleanData.birthday) {
      cleanData.birthday = new Date(cleanData.birthday);
    }

    return await prisma.clientProfile.update({
      where: { userId },
      data: cleanData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
        licenses: true,
      },
    });
  }

  /**
   * Update staff profile
   * @param userId - User ID
   * @param updateData - Data to update
   * @returns Updated staff profile
   */
  private async updateStaffProfile(userId: number, updateData: any) {
    const cleanData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    return await prisma.staffProfile.update({
      where: { userId },
      data: cleanData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Update mechanic profile
   * @param userId - User ID
   * @param updateData - Data to update
   * @returns Updated mechanic profile
   */
  private async updateMechanicProfile(userId: number, updateData: any) {
    const cleanData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    return await prisma.mechanicProfile.update({
      where: { userId },
      data: cleanData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Delete profile and associated user
   * @param userId - User ID to delete
   * @returns Deletion result
   */
  async deleteProfile(userId: number) {
    try {
      // Delete user (profiles will be deleted due to cascade)
      const deletedUser = await prisma.user.delete({
        where: { id: userId },
        include: {
          clientProfile: true,
          staffProfile: true,
          mechanicProfile: true,
        },
      });

      return deletedUser;
    } catch (error) {
      console.error('Error deleting profile:', error);
      throw new Error('Failed to delete profile');
    }
  }

  /**
   * Check if profile exists for user
   * @param userId - User ID
   * @returns Profile existence status
   */
  async profileExists(userId: number): Promise<{
    exists: boolean;
    profileType?: string;
    profileId?: number;
  }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          clientProfile: true,
          staffProfile: true,
          mechanicProfile: true,
        },
      });

      if (!user) {
        return { exists: false };
      }

      if (user.clientProfile) {
        return {
          exists: true,
          profileType: 'client',
          profileId: user.clientProfile.id,
        };
      }

      if (user.staffProfile) {
        return {
          exists: true,
          profileType: 'staff',
          profileId: user.staffProfile.id,
        };
      }

      if (user.mechanicProfile) {
        return {
          exists: true,
          profileType: 'mechanic',
          profileId: user.mechanicProfile.id,
        };
      }

      return { exists: false };
    } catch (error) {
      console.error('Error checking profile existence:', error);
      throw new Error('Failed to check profile existence');
    }
  }
}

// Export singleton instance
export const profileService = new ProfileService();
