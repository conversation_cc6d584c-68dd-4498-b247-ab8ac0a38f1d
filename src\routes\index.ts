import express, { Router } from "express";
import healthRoutes from "./healthRoutes.js";
import authRoutes from "../modules/auth/auth.route.js";
import profileRoutes from "../modules/profile/profile.route.js";

const router: Router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "An error occurred"
 *         error:
 *           type: string
 *           example: "Detailed error message"
 *         statusCode:
 *           type: number
 *           example: 400
 *     SuccessResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Operation completed successfully"
 *         data:
 *           type: object
 *           description: "Response data"
 */

// API Routes
router.use("/health", healthRoutes);
router.use("/auth", authRoutes);
router.use("/profile", profileRoutes);

/**
 * @swagger
 * /api:
 *   get:
 *     tags:
 *       - General
 *     summary: API root endpoint
 *     description: Returns basic API information and available endpoints
 *     responses:
 *       200:
 *         description: API information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Lion Car Rentals API v1.0.0"
 *                 documentation:
 *                   type: string
 *                   example: "/api-docs"
 *                 endpoints:
 *                   type: object
 *                   properties:
 *                     health:
 *                       type: string
 *                       example: "/api/health"
 *                     auth:
 *                       type: string
 *                       example: "/api/auth"
 *                     profile:
 *                       type: string
 *                       example: "/api/profile"
 */

router.get("/", (req, res) => {
  res.json({
    message: "Lion Car Rentals API v1.0.0",
    version: "1.0.0",
    documentation: "/api-docs",
    endpoints: {
      health: "/api/health",
      auth: "/api/auth",
      profile: "/api/profile"
    },
    features: [
      "Role-based authentication",
      "JWT token management",
      "Profile management",
      "Permission-based access control",
      "Rate limiting",
      "Comprehensive API documentation"
    ],
    timestamp: new Date().toISOString()
  });
});


export default router;