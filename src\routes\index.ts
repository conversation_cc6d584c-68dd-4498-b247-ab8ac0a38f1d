import express, { Router } from "express";
import healthRoutes from "./healthRoutes.js";
// import sample from "./"

const router: Router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "An error occurred"
 *         error:
 *           type: string
 *           example: "Detailed error message"
 *         statusCode:
 *           type: number
 *           example: 400
 *     SuccessResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Operation completed successfully"
 *         data:
 *           type: object
 *           description: "Response data"
 */

// API Routes
router.use("/health", healthRoutes);
// router.use("/sample", sample);

/**
 * @swagger
 * /api:
 *   get:
 *     tags:
 *       - General
 *     summary: API root endpoint
 *     description: Returns basic API information
 *     responses:
 *       200:
 *         description: API information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Lion Car Rentals API v1.0.0"
 *                 documentation:
 *                   type: string
 *                   example: "/api-docs"
 */
router.get("/", (req, res) => {
  res.json({
    message: "Lion Car Rentals API v1.0.0",
    documentation: "/api-docs",
    health: "/api/health"
  });
});

export default router;