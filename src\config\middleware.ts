import { authenticate, optionalAuth, strictAuth } from '../middleware/authenticate';
import { 
  requireRole, 
  requirePermission, 
  requireAnyPermission, 
  requireOwnership, 
  requireProfileType,
  conditionalAuth,
  rateLimitByRole
} from '../middleware/authorize';
import { PERMISSIONS } from './permissions';

/**
 * Centralized Middleware Configuration
 * Provides pre-configured middleware combinations for common use cases
 * Implements Dependency Inversion Principle by abstracting middleware dependencies
 */

// ===== AUTHENTICATION MIDDLEWARE =====
export { authenticate, optionalAuth, strictAuth };

// ===== AUTHORIZATION MIDDLEWARE =====
export { 
  requireRole, 
  requirePermission, 
  requireAnyPermission, 
  requireOwnership, 
  requireProfileType,
  conditionalAuth,
  rateLimitByRole
};

// ===== COMMON MIDDLEWARE COMBINATIONS =====

/**
 * Admin-only access
 * Requires authentication and ADMIN role
 */
export const adminOnly = [
  authenticate,
  requireRole('ADMIN')
];

/**
 * Staff-level access (Admin, Reception, Team Leader)
 * Requires authentication and staff-level role
 */
export const staffOnly = [
  authenticate,
  requireRole('ADMIN', 'RECEPTION', 'TEAM_LEADER')
];

/**
 * Client-only access
 * Requires authentication and CLIENT role
 */
export const clientOnly = [
  authenticate,
  requireRole('CLIENT')
];

/**
 * Mechanic-only access
 * Requires authentication and MECHANIC role
 */
export const mechanicOnly = [
  authenticate,
  requireRole('MECHANIC')
];

/**
 * Reception desk access (Reception and Admin)
 * For front desk operations
 */
export const receptionAccess = [
  authenticate,
  requireRole('ADMIN', 'RECEPTION')
];

/**
 * Management access (Admin and Team Leader)
 * For management-level operations
 */
export const managementAccess = [
  authenticate,
  requireRole('ADMIN', 'TEAM_LEADER')
];

/**
 * Assistance access (Assistance and higher roles)
 * For customer support operations
 */
export const assistanceAccess = [
  authenticate,
  requireRole('ADMIN', 'TEAM_LEADER', 'RECEPTION', 'ASSISTANCE')
];

// ===== PERMISSION-BASED COMBINATIONS =====

/**
 * User management permissions
 * Can create, read, update, delete users
 */
export const userManagement = [
  authenticate,
  requirePermission(
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE
  )
];

/**
 * Profile read access (own or all)
 * Can read own profile or all profiles based on role
 */
export const profileRead = [
  authenticate,
  requireAnyPermission(
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_READ_ALL
  )
];

/**
 * Profile update access (own or all)
 * Can update own profile or all profiles based on role
 */
export const profileUpdate = [
  authenticate,
  requireAnyPermission(
    PERMISSIONS.PROFILE_UPDATE_OWN,
    PERMISSIONS.PROFILE_UPDATE_ALL
  )
];

/**
 * Car management permissions
 * Can manage car inventory
 */
export const carManagement = [
  authenticate,
  requirePermission(
    PERMISSIONS.CAR_CREATE,
    PERMISSIONS.CAR_UPDATE,
    PERMISSIONS.CAR_DELETE
  )
];

/**
 * Car read access
 * Can view car information
 */
export const carRead = [
  authenticate,
  requirePermission(PERMISSIONS.CAR_READ)
];

/**
 * Rental management permissions
 * Can manage all rental operations
 */
export const rentalManagement = [
  authenticate,
  requirePermission(
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_UPDATE,
    PERMISSIONS.RENTAL_DELETE
  )
];

/**
 * Rental read access (own or all)
 * Can read own rentals or all rentals based on role
 */
export const rentalRead = [
  authenticate,
  requireAnyPermission(
    PERMISSIONS.RENTAL_READ_OWN,
    PERMISSIONS.RENTAL_READ_ALL
  )
];

// ===== OWNERSHIP-BASED COMBINATIONS =====

/**
 * Own profile access
 * Can only access own profile data
 */
export const ownProfileOnly = [
  authenticate,
  requireOwnership('userId')
];

/**
 * Own rental access
 * Can only access own rental data
 */
export const ownRentalOnly = [
  authenticate,
  requireOwnership('userId', { paramLocation: 'body' })
];

// ===== PROFILE TYPE COMBINATIONS =====

/**
 * Client profile required
 * Must have a complete client profile
 */
export const clientProfileRequired = [
  authenticate,
  requireProfileType('client')
];

/**
 * Staff profile required
 * Must have a complete staff profile
 */
export const staffProfileRequired = [
  authenticate,
  requireProfileType('staff')
];

/**
 * Mechanic profile required
 * Must have a complete mechanic profile
 */
export const mechanicProfileRequired = [
  authenticate,
  requireProfileType('mechanic')
];

// ===== RATE LIMITING COMBINATIONS =====

/**
 * Standard rate limiting by role
 * Different limits for different user types
 */
export const standardRateLimit = rateLimitByRole({
  CLIENT: 100,        // 100 requests per 15 minutes for clients
  RECEPTION: 200,     // 200 requests per 15 minutes for reception
  MECHANIC: 150,      // 150 requests per 15 minutes for mechanics
  TEAM_LEADER: 300,   // 300 requests per 15 minutes for team leaders
  ASSISTANCE: 200,    // 200 requests per 15 minutes for assistance
  ADMIN: 500,         // 500 requests per 15 minutes for admins
});

/**
 * Strict rate limiting for sensitive operations
 * Lower limits for sensitive endpoints
 */
export const strictRateLimit = rateLimitByRole({
  CLIENT: 20,         // 20 requests per 15 minutes for clients
  RECEPTION: 50,      // 50 requests per 15 minutes for reception
  MECHANIC: 30,       // 30 requests per 15 minutes for mechanics
  TEAM_LEADER: 100,   // 100 requests per 15 minutes for team leaders
  ASSISTANCE: 50,     // 50 requests per 15 minutes for assistance
  ADMIN: 200,         // 200 requests per 15 minutes for admins
});

// ===== CONDITIONAL COMBINATIONS =====

/**
 * Self or admin access
 * Users can access their own data, admins can access all data
 */
export const selfOrAdmin = (resourceField: string = 'userId') => [
  authenticate,
  conditionalAuth(
    (req) => req.user?.role === 'ADMIN',
    (req, res, next) => next(), // Admin can access anything
    requireOwnership(resourceField) // Non-admin must own resource
  )
];

/**
 * Business hours access
 * Only allow access during business hours (9 AM - 6 PM)
 */
export const businessHoursOnly = [
  authenticate,
  (req: any, res: any, next: any) => {
    const now = new Date();
    const hour = now.getHours();
    
    // Allow admin access anytime
    if (req.user?.role === 'ADMIN') {
      return next();
    }
    
    // Check business hours (9 AM - 6 PM)
    if (hour >= 9 && hour < 18) {
      return next();
    }
    
    return res.status(403).json({
      error: 'Access denied outside business hours',
      code: 'OUTSIDE_BUSINESS_HOURS',
      businessHours: '9:00 AM - 6:00 PM',
      currentTime: now.toISOString()
    });
  }
];

// ===== HELPER FUNCTIONS =====

/**
 * Create custom middleware combination
 * @param middlewares - Array of middleware functions
 * @returns Combined middleware array
 */
export const combine = (...middlewares: any[]) => {
  return middlewares.flat();
};

/**
 * Create role-specific middleware
 * @param roles - Allowed roles
 * @param permissions - Required permissions (optional)
 * @returns Middleware array
 */
export const createRoleMiddleware = (roles: any[], permissions?: string[]) => {
  const middleware = [authenticate, requireRole(...roles)];
  
  if (permissions && permissions.length > 0) {
    middleware.push(requirePermission(...permissions));
  }
  
  return middleware;
};

/**
 * Create ownership middleware with role override
 * @param resourceField - Field containing resource owner ID
 * @param overrideRoles - Roles that can override ownership check
 * @returns Middleware array
 */
export const createOwnershipMiddleware = (
  resourceField: string = 'userId',
  overrideRoles: any[] = ['ADMIN']
) => {
  return [
    authenticate,
    requireOwnership(resourceField, { 
      adminOverride: true, 
      allowedRoles: overrideRoles 
    })
  ];
};
