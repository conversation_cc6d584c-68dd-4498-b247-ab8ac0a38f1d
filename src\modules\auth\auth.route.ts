import express from 'express';


import { register } from './auth.controller';
const router = express.Router();

router.post('/register', register);

import { login } from './auth.controller';

router.post('/login', login);


import { authenticate } from '../../middleware/authenticate';

router.get('/protected', authenticate, (req, res) => {
    // TypeScript fix: explicitly type req as having a 'user' property
    const user = (req as typeof req & { user?: any }).user;
    res.json({ message: 'This is protected', user });
});