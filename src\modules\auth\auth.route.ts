import express from 'express';
import { authController } from './auth.controller';
import {
  authenticate,
  adminOnly,
  requireRole,
  requirePermission,
  standardRateLimit,
  strictRateLimit
} from '../../config/middleware';
import { PERMISSIONS } from '../../config/permissions';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Authentication
 *   description: User authentication and registration endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User email address
 *         password:
 *           type: string
 *           description: User password
 *
 *     AuthResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         token:
 *           type: string
 *           description: JWT access token
 *         refreshToken:
 *           type: string
 *           description: JWT refresh token
 *         user:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             email:
 *               type: string
 *             role:
 *               type: string
 *               enum: [CLIENT, ADMIN, RECEP<PERSON><PERSON>, MECHAN<PERSON>, TEAM_LEADER, ASSISTANCE]
 *             profileType:
 *               type: string
 *               enum: [client, staff, mechanic]
 *             permissions:
 *               type: array
 *               items:
 *                 type: string
 *
 *     ClientRegistrationRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *         - firstName
 *         - lastName
 *         - address
 *         - postCode
 *         - country
 *         - phoneNumber
 *         - birthday
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 6
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         address:
 *           type: string
 *         postCode:
 *           type: string
 *         country:
 *           type: string
 *         phoneNumber:
 *           type: string
 *         birthday:
 *           type: string
 *           format: date
 *         isCorporate:
 *           type: boolean
 *           default: false
 *         companyName:
 *           type: string
 *         emergencyContactPersonName:
 *           type: string
 *         emergencyContactRelationship:
 *           type: string
 *         emergencyContactPhoneNumber:
 *           type: string
 */

// ===== PUBLIC ROUTES =====

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     tags: [Authentication]
 *     summary: User login
 *     description: Authenticate user and return JWT tokens
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Server error
 */
router.post('/login',
  standardRateLimit,
  authController.login.bind(authController)
);

/**
 * @swagger
 * /api/auth/register/client:
 *   post:
 *     tags: [Authentication]
 *     summary: Register new client
 *     description: Register a new client user with profile
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ClientRegistrationRequest'
 *     responses:
 *       201:
 *         description: Client registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Invalid request data
 *       409:
 *         description: User already exists
 *       500:
 *         description: Server error
 */
router.post('/register/client',
  strictRateLimit,
  authController.registerClient.bind(authController)
);

// ===== ADMIN-ONLY REGISTRATION ROUTES =====

/**
 * @swagger
 * /api/auth/register/staff:
 *   post:
 *     tags: [Authentication]
 *     summary: Register new staff member
 *     description: Register a new staff member (Admin only)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - role
 *               - employeeId
 *               - firstName
 *               - lastName
 *               - phoneNumber
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *               role:
 *                 type: string
 *                 enum: [ADMIN, RECEPTION, TEAM_LEADER, ASSISTANCE]
 *               employeeId:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               position:
 *                 type: string
 *     responses:
 *       201:
 *         description: Staff member registered successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       409:
 *         description: User already exists
 *       500:
 *         description: Server error
 */
router.post('/register/staff',
  ...adminOnly,
  strictRateLimit,
  authController.registerStaff.bind(authController)
);

/**
 * @swagger
 * /api/auth/register/mechanic:
 *   post:
 *     tags: [Authentication]
 *     summary: Register new mechanic
 *     description: Register a new mechanic (Admin only)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - employeeId
 *               - firstName
 *               - lastName
 *               - phoneNumber
 *               - certificationNumber
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *               employeeId:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               certificationNumber:
 *                 type: string
 *               specialization:
 *                 type: string
 *     responses:
 *       201:
 *         description: Mechanic registered successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       409:
 *         description: User already exists
 *       500:
 *         description: Server error
 */
router.post('/register/mechanic',
  ...adminOnly,
  strictRateLimit,
  authController.registerMechanic.bind(authController)
);

// ===== TOKEN MANAGEMENT ROUTES =====

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     tags: [Authentication]
 *     summary: Refresh access token
 *     description: Get a new access token using refresh token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Valid refresh token
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *       400:
 *         description: Refresh token required
 *       401:
 *         description: Invalid or expired refresh token
 *       500:
 *         description: Server error
 */
router.post('/refresh',
  standardRateLimit,
  authController.refreshToken.bind(authController)
);

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     tags: [Authentication]
 *     summary: Logout user
 *     description: Logout user (client-side token removal)
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Server error
 */
router.post('/logout',
  authenticate,
  authController.logout.bind(authController)
);

// ===== PROTECTED TEST ROUTES =====

/**
 * @swagger
 * /api/auth/protected:
 *   get:
 *     tags: [Authentication]
 *     summary: Test protected route
 *     description: Test endpoint to verify authentication
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Access granted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *                 permissions:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Server error
 */
router.get('/protected', authenticate, (req, res) => {
  res.json({
    message: 'Access granted to protected route',
    user: req.user,
    permissions: req.permissions,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/auth/admin-only:
 *   get:
 *     tags: [Authentication]
 *     summary: Test admin-only route
 *     description: Test endpoint to verify admin access
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin access granted
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.get('/admin-only', ...adminOnly, (req, res) => {
  res.json({
    message: 'Admin access granted',
    user: req.user,
    timestamp: new Date().toISOString()
  });
});

export default router;