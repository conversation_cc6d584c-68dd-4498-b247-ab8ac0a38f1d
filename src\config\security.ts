import { Request, Response, NextFunction } from 'express';
import { Role } from '../../generated/prisma';

/**
 * Security Configuration and Utilities
 * Centralized security settings and helper functions
 */

// ===== SECURITY CONSTANTS =====

export const SECURITY_CONFIG = {
  // JWT Configuration
  JWT: {
    DEFAULT_EXPIRES_IN: '24h',
    REFRESH_EXPIRES_IN: '7d',
    ALGORITHM: 'HS256',
    ISSUER: 'lion-car-rentals',
    AUDIENCE: 'lion-car-rentals-api',
  },

  // Password Requirements
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: false,
    REQUIRE_SYMBOLS: false,
    SALT_ROUNDS: 12,
  },

  // Rate Limiting
  RATE_LIMITS: {
    STANDARD: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      CLIENT: 100,
      RECEPTION: 200,
      MECHANIC: 150,
      TEAM_LEADER: 300,
      ASSISTANCE: 200,
      ADMIN: 500,
    },
    STRICT: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      CLIENT: 20,
      RECEPTION: 50,
      MECHANIC: 30,
      TEAM_LEADER: 100,
      ASSISTANCE: 50,
      ADMIN: 200,
    },
    AUTH: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      MAX_ATTEMPTS: 5, // Max login attempts per IP
    },
  },

  // Session Management
  SESSION: {
    MAX_CONCURRENT_SESSIONS: 3,
    IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes
    ABSOLUTE_TIMEOUT: 8 * 60 * 60 * 1000, // 8 hours
  },

  // CORS Configuration
  CORS: {
    ALLOWED_ORIGINS: [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://lioncarrentals.com',
    ],
    ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    ALLOWED_HEADERS: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
    ],
    CREDENTIALS: true,
  },

  // Security Headers
  HEADERS: {
    CONTENT_SECURITY_POLICY: "default-src 'self'",
    X_FRAME_OPTIONS: 'DENY',
    X_CONTENT_TYPE_OPTIONS: 'nosniff',
    REFERRER_POLICY: 'strict-origin-when-cross-origin',
    PERMISSIONS_POLICY: 'geolocation=(), microphone=(), camera=()',
  },
} as const;

// ===== SECURITY VALIDATION FUNCTIONS =====

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns Validation result
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  const config = SECURITY_CONFIG.PASSWORD;

  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }

  if (password.length < config.MIN_LENGTH) {
    errors.push(`Password must be at least ${config.MIN_LENGTH} characters long`);
  }

  if (password.length > config.MAX_LENGTH) {
    errors.push(`Password must be no more than ${config.MAX_LENGTH} characters long`);
  }

  if (config.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (config.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (config.REQUIRE_NUMBERS && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (config.REQUIRE_SYMBOLS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate email format
 * @param email - Email to validate
 * @returns Whether email is valid
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format
 * @param phone - Phone number to validate
 * @returns Whether phone number is valid
 */
export const validatePhoneNumber = (phone: string): boolean => {
  // Basic phone validation - can be enhanced based on requirements
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
};

/**
 * Sanitize input string
 * @param input - Input to sanitize
 * @returns Sanitized string
 */
export const sanitizeInput = (input: string): string => {
  if (!input) return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes to prevent injection
    .substring(0, 1000); // Limit length
};

// ===== SECURITY MIDDLEWARE =====

/**
 * Security headers middleware
 * Adds security headers to all responses
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  const headers = SECURITY_CONFIG.HEADERS;

  res.setHeader('Content-Security-Policy', headers.CONTENT_SECURITY_POLICY);
  res.setHeader('X-Frame-Options', headers.X_FRAME_OPTIONS);
  res.setHeader('X-Content-Type-Options', headers.X_CONTENT_TYPE_OPTIONS);
  res.setHeader('Referrer-Policy', headers.REFERRER_POLICY);
  res.setHeader('Permissions-Policy', headers.PERMISSIONS_POLICY);
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Remove server information
  res.removeHeader('X-Powered-By');

  next();
};

/**
 * Input sanitization middleware
 * Sanitizes request body, query, and params
 */
export const sanitizeRequest = (req: Request, res: Response, next: NextFunction): void => {
  // Sanitize body
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = sanitizeInput(req.body[key]);
      }
    }
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = sanitizeInput(req.query[key] as string);
      }
    }
  }

  // Sanitize route parameters
  if (req.params && typeof req.params === 'object') {
    for (const key in req.params) {
      if (typeof req.params[key] === 'string') {
        req.params[key] = sanitizeInput(req.params[key]);
      }
    }
  }

  next();
};

/**
 * Request size limiter middleware
 * Prevents large payload attacks
 */
export const requestSizeLimiter = (maxSize: string = '10mb') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('content-length');
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength);
      const maxSizeInBytes = parseSize(maxSize);
      
      if (sizeInBytes > maxSizeInBytes) {
        res.status(413).json({
          error: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
          maxSize,
        });
        return;
      }
    }
    
    next();
  };
};

/**
 * IP whitelist middleware
 * Restricts access to specific IP addresses
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    
    if (!clientIP || !allowedIPs.includes(clientIP)) {
      res.status(403).json({
        error: 'Access denied from this IP address',
        code: 'IP_NOT_ALLOWED',
      });
      return;
    }
    
    next();
  };
};

// ===== UTILITY FUNCTIONS =====

/**
 * Parse size string to bytes
 * @param size - Size string (e.g., '10mb', '1gb')
 * @returns Size in bytes
 */
const parseSize = (size: string): number => {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };

  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) return 0;

  const value = parseFloat(match[1]);
  const unit = match[2];

  return Math.floor(value * units[unit]);
};

/**
 * Generate secure random string
 * @param length - Length of string to generate
 * @returns Random string
 */
export const generateSecureRandom = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

/**
 * Check if request is from trusted source
 * @param req - Express request object
 * @returns Whether request is trusted
 */
export const isTrustedRequest = (req: Request): boolean => {
  // Check for API key
  const apiKey = req.headers['x-api-key'];
  if (apiKey && apiKey === process.env.API_KEY) {
    return true;
  }

  // Check for internal service headers
  const internalHeader = req.headers['x-internal-service'];
  if (internalHeader === 'true') {
    return true;
  }

  return false;
};

/**
 * Log security event
 * @param event - Security event details
 */
export const logSecurityEvent = (event: {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  userId?: number;
  ip?: string;
  userAgent?: string;
  additionalData?: any;
}): void => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    ...event,
  };

  // In production, this should go to a proper logging service
  console.log('SECURITY EVENT:', JSON.stringify(logEntry, null, 2));

  // For critical events, you might want to send alerts
  if (event.severity === 'critical') {
    // Send alert to monitoring system
    console.error('CRITICAL SECURITY EVENT:', logEntry);
  }
};
