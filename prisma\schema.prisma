// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


// All user roles for the application
enum Role {
  CLIENT
  ADMIN
  RECEPTION
  MECHANIC
  TEAM_LEADER
  ASSISTANCE
}


// --- CORE AUTHENTICATION MODEL ---
// Uses an auto-incrementing integer as the primary key.
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  role      Role     @default(CLIENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Links to the different profile types
  clientProfile   ClientProfile?
  staffProfile    StaffProfile?
  mechanicProfile MechanicProfile?
}


// --- PROFILE MODELS ---

// 1. Profile for Customers/Clients
model ClientProfile {
  id                         Int      @id @default(autoincrement())
  customerId                 String   @unique @default(cuid()) // Kept as cuid for a non-guessable public customer ID
  firstName                  String
  lastName                   String
  address                    String
  postCode                   String
  country                    String
  phoneNumber                String   @unique
  birthday                   DateTime
  isCorporate                Boolean  @default(false)
  companyName                String?
  emergencyContactPersonName   String?
  emergencyContactRelationship String?
  emergencyContactPhoneNumber  String?

  // Back-relation to the main User model. Note that userId is now an Int.
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int    @unique

  // A client has a history of one or more licenses.
  licenses DriverLicense[]
}

// 2. Generic Profile for most Staff roles (Admin, Reception, etc.)
model StaffProfile {
  id          Int     @id @default(autoincrement())
  employeeId  String  @unique
  firstName   String
  lastName    String
  phoneNumber String  @unique
  position    String?

  // Back-relation to the main User model. userId is an Int.
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int    @unique
}

// 3. Specialized Profile for Mechanics with unique fields
model MechanicProfile {
  id                  Int      @id @default(autoincrement())
  employeeId          String   @unique
  firstName           String
  lastName            String
  phoneNumber         String   @unique
  certificationNumber String
  specialization      String?
  
  // Back-relation to the main User model. userId is an Int.
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int    @unique
}


// --- CLIENT-RELATED DATA MODELS ---

model DriverLicense {
  id           Int      @id @default(autoincrement())
  dlNumber     String   @unique
  issueCountry String
  issueDate    DateTime
  expiryDate   DateTime
  conditions   String?
  frontViewUrl String?
  backViewUrl  String?
  
  // Link back to the ClientProfile. Note that profileId is now an Int.
  profile   ClientProfile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  profileId Int
}