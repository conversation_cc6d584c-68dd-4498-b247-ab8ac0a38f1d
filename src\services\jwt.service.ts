import jwt from 'jsonwebtoken';
import { Role } from '../../generated/prisma';
import { JWTPayload } from '../types/auth';

/**
 * JWT Service - Handles all JWT token operations
 * Implements Single Responsibility Principle by handling only JWT operations
 */
export class JWTService {
  private readonly secret: string;
  private readonly expiresIn: string;
  private readonly refreshExpiresIn: string;

  constructor() {
    this.secret = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production';
    this.expiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

    // Warn if using fallback secret in production
    if (process.env.NODE_ENV === 'production' && this.secret === 'fallback-secret-key-change-in-production') {
      console.warn('⚠️  WARNING: Using fallback JWT secret in production! Set JWT_SECRET environment variable.');
    }
  }

  /**
   * Generate access token with user information
   * @param payload - User information to encode in token
   * @returns Signed JWT token
   */
  generateToken(payload: {
    userId: number;
    email: string;
    role: Role;
    profileId?: number;
    profileType?: string;
  }): string {
    try {
      const tokenPayload = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
        ...(payload.profileId && { profileId: payload.profileId }),
        ...(payload.profileType && { profileType: payload.profileType as 'client' | 'staff' | 'mechanic' }),
      };

      return jwt.sign(tokenPayload, this.secret, {
        expiresIn: this.expiresIn,
        issuer: 'lion-car-rentals',
        audience: 'lion-car-rentals-api',
      } as jwt.SignOptions);
    } catch (error) {
      console.error('Error generating JWT token:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  /**
   * Generate refresh token (longer expiration)
   * @param payload - User information to encode in token
   * @returns Signed refresh token
   */
  generateRefreshToken(payload: {
    userId: number;
    email: string;
    role: Role;
  }): string {
    try {
      const tokenPayload = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
        type: 'refresh', // Mark as refresh token
      };

      return jwt.sign(tokenPayload, this.secret, {
        expiresIn: this.refreshExpiresIn,
        issuer: 'lion-car-rentals',
        audience: 'lion-car-rentals-api',
      } as jwt.SignOptions);
    } catch (error) {
      console.error('Error generating refresh token:', error);
      throw new Error('Failed to generate refresh token');
    }
  }

  /**
   * Verify and decode JWT token
   * @param token - JWT token to verify
   * @returns Decoded token payload
   * @throws Error if token is invalid or expired
   */
  verifyToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'lion-car-rentals',
        audience: 'lion-car-rentals-api',
      }) as JWTPayload;

      // Validate required fields
      if (!decoded.userId || !decoded.email || !decoded.role) {
        throw new Error('Invalid token payload: missing required fields');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else if (error instanceof jwt.NotBeforeError) {
        throw new Error('Token not active yet');
      } else {
        console.error('JWT verification error:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Verify refresh token
   * @param token - Refresh token to verify
   * @returns Decoded token payload
   */
  verifyRefreshToken(token: string): any {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'lion-car-rentals',
        audience: 'lion-car-rentals-api',
      }) as any;

      // Check if it's actually a refresh token
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid refresh token');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else {
        console.error('Refresh token verification error:', error);
        throw new Error('Refresh token verification failed');
      }
    }
  }

  /**
   * Refresh access token using refresh token
   * @param refreshToken - Valid refresh token
   * @returns New access token
   */
  refreshAccessToken(refreshToken: string): string {
    try {
      const decoded = this.verifyRefreshToken(refreshToken);

      // Generate new access token (without refresh token fields)
      const { type, iat, exp, ...payload } = decoded;
      return this.generateToken(payload);
    } catch (error) {
      console.error('Error refreshing access token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  /**
   * Decode token without verification (for debugging)
   * @param token - JWT token to decode
   * @returns Decoded token payload (unverified)
   */
  decodeToken(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Get token expiration time
   * @param token - JWT token
   * @returns Expiration timestamp or null
   */
  getTokenExpiration(token: string): number | null {
    try {
      const decoded = this.decodeToken(token);
      return decoded?.exp || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if token is expired
   * @param token - JWT token
   * @returns True if expired, false otherwise
   */
  isTokenExpired(token: string): boolean {
    try {
      const exp = this.getTokenExpiration(token);
      if (!exp) return true;

      return Date.now() >= exp * 1000; // Convert to milliseconds
    } catch (error) {
      return true;
    }
  }

  /**
   * Get time until token expires
   * @param token - JWT token
   * @returns Milliseconds until expiration, or 0 if expired/invalid
   */
  getTimeUntilExpiration(token: string): number {
    try {
      const exp = this.getTokenExpiration(token);
      if (!exp) return 0;

      const timeUntilExp = (exp * 1000) - Date.now();
      return Math.max(0, timeUntilExp);
    } catch (error) {
      return 0;
    }
  }
}

// Export singleton instance
export const jwtService = new JWTService();