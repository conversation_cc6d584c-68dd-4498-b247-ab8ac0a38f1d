import jwt from 'jsonwebtoken';
import { Role } from '@prisma/client';

export class JWTService {
  private readonly secret: string;
  private readonly expiresIn: string;

  constructor() {
    this.secret = process.env.JWT_SECRET || 'fallback-secret';
    this.expiresIn = process.env.JWT_EXPIRES_IN || '24h';
  }

  // Generate token with role and profile information
  generateToken(payload: {
    userId: number;
    email: string;
    role: Role;
    profileId?: number;
    profileType?: string;
  }): string {
    return jwt.sign(payload, this.secret, { expiresIn: this.expiresIn });
  }

  // Verify and decode token
  verifyToken(token: string): JWTPayload {
    return jwt.verify(token, this.secret) as JWTPayload;
  }

  // Refresh token logic
  refreshToken(oldToken: string): string {
    const decoded = this.verifyToken(oldToken);
    // Remove exp and iat from payload
    const { exp, iat, ...payload } = decoded;
    return this.generateToken(payload);
  }
}