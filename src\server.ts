import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import routes from "./routes/index.js";
import { errorHandler } from "./middleware/errorHandler.js";
import { createSwaggerSetup } from "./config/swagger.js";
import { connectDB } from "./config/db";

dotenv.config();

// // Connect to DB before starting server
 await connectDB();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));


// Setup Swagger documentation
// Follows Dependency Inversion Principle by using abstraction
const swaggerSetup = createSwaggerSetup();
swaggerSetup.setupSwagger(app);

// Routes
app.use("/api", routes);

// Global error handler
app.use(errorHandler);

// Start server
const PORT: number = 5001;
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
