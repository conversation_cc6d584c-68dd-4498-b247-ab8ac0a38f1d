import { Request, Response, NextFunction } from 'express';
import { JWTService } from '../services/jwt.service';
import { ROLE_PERMISSIONS } from '../config/permissions';

/**
 * Enhanced Authentication Middleware with Permission Loading
 * Implements Single Responsibility Principle by handling only authentication
 */
class AuthMiddleware {
  private jwtService: JWTService;

  constructor() {
    this.jwtService = new JWTService();
  }

  /**
   * Standard authentication middleware
   * Verifies JWT token and attaches user info to request
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   */
  authenticate = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          error: 'No valid token provided',
          code: 'MISSING_TOKEN',
          message: 'Authorization header must contain a valid Bearer token'
        });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      if (!token) {
        res.status(401).json({
          error: 'Token is required',
          code: 'EMPTY_TOKEN'
        });
        return;
      }

      // Verify token using JWT service
      const decoded = this.jwtService.verifyToken(token);

      // Attach user and permissions to request
      req.user = decoded;
      req.permissions = ROLE_PERMISSIONS[decoded.role] || [];

      next();
    } catch (error) {
      console.error('Authentication error:', error);

      // Handle specific JWT errors
      if (error instanceof Error) {
        if (error.message.includes('expired')) {
          res.status(401).json({
            error: 'Token has expired',
            code: 'TOKEN_EXPIRED',
            message: 'Please login again to get a new token'
          });
          return;
        } else if (error.message.includes('invalid')) {
          res.status(401).json({
            error: 'Invalid token',
            code: 'INVALID_TOKEN'
          });
          return;
        }
      }

      res.status(401).json({
        error: 'Authentication failed',
        code: 'AUTH_FAILED'
      });
    }
  };

  /**
   * Optional authentication middleware
   * Attaches user info if token is present and valid, but doesn't require it
   * Useful for endpoints that work differently for authenticated vs anonymous users
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   */
  optionalAuth = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const authHeader = req.headers.authorization;

      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);

        if (token) {
          try {
            const decoded = this.jwtService.verifyToken(token);
            req.user = decoded;
            req.permissions = ROLE_PERMISSIONS[decoded.role] || [];
          } catch (error) {
            // Silently fail for optional auth - just continue without user info
            console.warn('Optional auth failed:', error);
          }
        }
      }

      next();
    } catch (error) {
      // For optional auth, we continue even if there's an error
      console.warn('Optional authentication error:', error);
      next();
    }
  };

  /**
   * Strict authentication middleware with additional validation
   * Performs extra checks beyond basic token validation
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   */
  strictAuth = (req: Request, res: Response, next: NextFunction): void => {
    try {
      // First, perform standard authentication
      this.authenticate(req, res, (error) => {
        if (error) {
          return next(error);
        }

        // Additional strict validation
        if (!req.user) {
          res.status(401).json({
            error: 'Authentication required',
            code: 'AUTH_REQUIRED'
          });
          return;
        }

        // Check if user has a valid profile
        if (!req.user.profileId && req.user.role !== 'ADMIN') {
          res.status(403).json({
            error: 'Complete profile required',
            code: 'PROFILE_INCOMPLETE',
            message: 'Please complete your profile to access this resource'
          });
          return;
        }

        // Check token expiration time (warn if expiring soon)
        const timeUntilExpiration = this.jwtService.getTimeUntilExpiration(
          req.headers.authorization!.substring(7)
        );

        if (timeUntilExpiration < 5 * 60 * 1000) { // Less than 5 minutes
          res.set('X-Token-Expiring', 'true');
          res.set('X-Token-Expires-In', timeUntilExpiration.toString());
        }

        next();
      });
    } catch (error) {
      console.error('Strict authentication error:', error);
      res.status(500).json({
        error: 'Authentication service error',
        code: 'AUTH_SERVICE_ERROR'
      });
    }
  };
}

// Export singleton instance
const authMiddleware = new AuthMiddleware();

// Export middleware functions for easy use
export const authenticate = authMiddleware.authenticate;
export const optionalAuth = authMiddleware.optionalAuth;
export const strictAuth = authMiddleware.strictAuth;

// Export class for advanced usage
export { AuthMiddleware };