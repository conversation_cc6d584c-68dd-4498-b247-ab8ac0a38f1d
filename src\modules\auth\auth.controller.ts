import bcrypt from 'bcryptjs';
import { prisma } from '../../config/db';
import { Request, Response } from 'express';
import { JWTService } from '../../services/jwt.service';
import { ROLE_PERMISSIONS } from '../../config/permissions';
import {
  AuthResponse,
  ClientRegistrationRequest,
  StaffRegistrationRequest,
  MechanicRegistrationRequest,
  LoginRequest
} from '../../types/auth';


/**
 * Enhanced Authentication Controller with Role-Based Access Control
 * Implements Single Responsibility Principle by handling only authentication operations
 */
export class AuthController {
  private jwtService: JWTService;

  constructor() {
    this.jwtService = new JWTService();
  }

  /**
   * Enhanced login with role and profile data
   * @param req - Express request object
   * @param res - Express response object
   */
  async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginRequest = req.body;

      // Validate input
      if (!email || !password) {
        res.status(400).json({ error: 'Email and password are required' });
        return;
      }

      // Find user with profile relations
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          clientProfile: true,
          staffProfile: true,
          mechanicProfile: true,
        },
      });

      if (!user) {
        res.status(401).json({ error: 'Invalid credentials' });
        return;
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        res.status(401).json({ error: 'Invalid credentials' });
        return;
      }

      // Determine profile type and ID
      let profileId: number | undefined;
      let profileType: string | undefined;

      if (user.clientProfile) {
        profileId = user.clientProfile.id;
        profileType = 'client';
      } else if (user.staffProfile) {
        profileId = user.staffProfile.id;
        profileType = 'staff';
      } else if (user.mechanicProfile) {
        profileId = user.mechanicProfile.id;
        profileType = 'mechanic';
      }

      // Generate tokens
      const token = this.jwtService.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
        ...(profileId && { profileId }),
        ...(profileType && { profileType }),
      });

      const refreshToken = this.jwtService.generateRefreshToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      });

      // Get user permissions
      const permissions = ROLE_PERMISSIONS[user.role] || [];

      const response: AuthResponse = {
        message: 'Login successful',
        token,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          ...(profileType && { profileType }),
          permissions,
        },
      };

      res.json(response);
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ error: 'Login failed' });
    }
  }

  /**
   * Register a new client with profile
   * @param req - Express request object
   * @param res - Express response object
   */
  async registerClient(req: Request, res: Response): Promise<void> {
    try {
      const {
        email,
        password,
        firstName,
        lastName,
        address,
        postCode,
        country,
        phoneNumber,
        birthday,
        isCorporate = false,
        companyName,
        emergencyContactPersonName,
        emergencyContactRelationship,
        emergencyContactPhoneNumber,
      }: ClientRegistrationRequest = req.body;

      // Validate required fields
      if (!email || !password || !firstName || !lastName || !address || !postCode || !country || !phoneNumber || !birthday) {
        res.status(400).json({
          error: 'Missing required fields: email, password, firstName, lastName, address, postCode, country, phoneNumber, birthday'
        });
        return;
      }

      // Validate password strength
      if (password.length < 6) {
        res.status(400).json({ error: 'Password must be at least 6 characters long' });
        return;
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({ where: { email } });
      if (existingUser) {
        res.status(409).json({ error: 'User with this email already exists' });
        return;
      }

      // Check if phone number already exists
      const existingPhone = await prisma.clientProfile.findUnique({ where: { phoneNumber } });
      if (existingPhone) {
        res.status(409).json({ error: 'User with this phone number already exists' });
        return;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user and profile in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email,
            password: hashedPassword,
            role: 'CLIENT',
          },
        });

        // Create client profile
        const profile = await tx.clientProfile.create({
          data: {
            userId: user.id,
            firstName,
            lastName,
            address,
            postCode,
            country,
            phoneNumber,
            birthday: new Date(birthday),
            isCorporate,
            ...(companyName && { companyName }),
            ...(emergencyContactPersonName && { emergencyContactPersonName }),
            ...(emergencyContactRelationship && { emergencyContactRelationship }),
            ...(emergencyContactPhoneNumber && { emergencyContactPhoneNumber }),
          },
        });

        return { user, profile };
      });

      // Generate tokens
      const token = this.jwtService.generateToken({
        userId: result.user.id,
        email: result.user.email,
        role: result.user.role,
        profileId: result.profile.id,
        profileType: 'client',
      });

      const refreshToken = this.jwtService.generateRefreshToken({
        userId: result.user.id,
        email: result.user.email,
        role: result.user.role,
      });

      // Get user permissions
      const permissions = ROLE_PERMISSIONS[result.user.role] || [];

      const response: AuthResponse = {
        message: 'Client registered successfully',
        token,
        refreshToken,
        user: {
          id: result.user.id,
          email: result.user.email,
          role: result.user.role,
          profileType: 'client',
          permissions,
        },
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Client registration error:', error);
      res.status(500).json({ error: 'Registration failed' });
    }
  }

  /**
   * Register a new staff member (Admin only)
   * @param req - Express request object
   * @param res - Express response object
   */
  async registerStaff(req: Request, res: Response): Promise<void> {
    try {
      const {
        email,
        password,
        role,
        employeeId,
        firstName,
        lastName,
        phoneNumber,
        position,
      }: StaffRegistrationRequest = req.body;

      // Validate required fields
      if (!email || !password || !role || !employeeId || !firstName || !lastName || !phoneNumber) {
        res.status(400).json({
          error: 'Missing required fields: email, password, role, employeeId, firstName, lastName, phoneNumber'
        });
        return;
      }

      // Validate role
      const validStaffRoles = ['ADMIN', 'RECEPTION', 'TEAM_LEADER', 'ASSISTANCE'];
      if (!validStaffRoles.includes(role)) {
        res.status(400).json({ error: 'Invalid role for staff registration' });
        return;
      }

      // Validate password strength
      if (password.length < 6) {
        res.status(400).json({ error: 'Password must be at least 6 characters long' });
        return;
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({ where: { email } });
      if (existingUser) {
        res.status(409).json({ error: 'User with this email already exists' });
        return;
      }

      // Check if employee ID already exists
      const existingEmployee = await prisma.staffProfile.findUnique({ where: { employeeId } });
      if (existingEmployee) {
        res.status(409).json({ error: 'Employee ID already exists' });
        return;
      }

      // Check if phone number already exists
      const existingPhone = await prisma.staffProfile.findUnique({ where: { phoneNumber } });
      if (existingPhone) {
        res.status(409).json({ error: 'Phone number already exists' });
        return;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user and profile in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email,
            password: hashedPassword,
            role: role as any,
          },
        });

        // Create staff profile
        const profile = await tx.staffProfile.create({
          data: {
            userId: user.id,
            employeeId,
            firstName,
            lastName,
            phoneNumber,
            ...(position && { position }),
          },
        });

        return { user, profile };
      });

      res.status(201).json({
        message: 'Staff member registered successfully',
        user: {
          id: result.user.id,
          email: result.user.email,
          role: result.user.role,
          employeeId: result.profile.employeeId,
        },
      });
    } catch (error) {
      console.error('Staff registration error:', error);
      res.status(500).json({ error: 'Registration failed' });
    }
  }

  /**
   * Register a new mechanic (Admin only)
   * @param req - Express request object
   * @param res - Express response object
   */
  async registerMechanic(req: Request, res: Response): Promise<void> {
    try {
      const {
        email,
        password,
        employeeId,
        firstName,
        lastName,
        phoneNumber,
        certificationNumber,
        specialization,
      }: MechanicRegistrationRequest = req.body;

      // Validate required fields
      if (!email || !password || !employeeId || !firstName || !lastName || !phoneNumber || !certificationNumber) {
        res.status(400).json({
          error: 'Missing required fields: email, password, employeeId, firstName, lastName, phoneNumber, certificationNumber'
        });
        return;
      }

      // Validate password strength
      if (password.length < 6) {
        res.status(400).json({ error: 'Password must be at least 6 characters long' });
        return;
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({ where: { email } });
      if (existingUser) {
        res.status(409).json({ error: 'User with this email already exists' });
        return;
      }

      // Check if employee ID already exists
      const existingEmployee = await prisma.mechanicProfile.findUnique({ where: { employeeId } });
      if (existingEmployee) {
        res.status(409).json({ error: 'Employee ID already exists' });
        return;
      }

      // Check if phone number already exists
      const existingPhone = await prisma.mechanicProfile.findUnique({ where: { phoneNumber } });
      if (existingPhone) {
        res.status(409).json({ error: 'Phone number already exists' });
        return;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user and profile in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email,
            password: hashedPassword,
            role: 'MECHANIC',
          },
        });

        // Create mechanic profile
        const profile = await tx.mechanicProfile.create({
          data: {
            userId: user.id,
            employeeId,
            firstName,
            lastName,
            phoneNumber,
            certificationNumber,
            ...(specialization && { specialization }),
          },
        });

        return { user, profile };
      });

      res.status(201).json({
        message: 'Mechanic registered successfully',
        user: {
          id: result.user.id,
          email: result.user.email,
          role: result.user.role,
          employeeId: result.profile.employeeId,
          certificationNumber: result.profile.certificationNumber,
        },
      });
    } catch (error) {
      console.error('Mechanic registration error:', error);
      res.status(500).json({ error: 'Registration failed' });
    }
  }

  /**
   * Refresh access token using refresh token
   * @param req - Express request object
   * @param res - Express response object
   */
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json({ error: 'Refresh token is required' });
        return;
      }

      // Generate new access token
      const newAccessToken = this.jwtService.refreshAccessToken(refreshToken);

      res.json({
        message: 'Token refreshed successfully',
        token: newAccessToken,
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({ error: 'Invalid or expired refresh token' });
    }
  }

  /**
   * Logout user (client-side token removal, server-side could implement token blacklisting)
   * @param req - Express request object
   * @param res - Express response object
   */
  async logout(req: Request, res: Response): Promise<void> {
    try {
      // In a more advanced implementation, you would blacklist the token here
      // For now, we just return a success message as the client will remove the token
      res.json({
        message: 'Logged out successfully',
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({ error: 'Logout failed' });
    }
  }
}

// Export singleton instance for use in routes
export const authController = new AuthController();

// Export individual functions for backward compatibility
export const login = authController.login.bind(authController);
export const register = authController.registerClient.bind(authController);