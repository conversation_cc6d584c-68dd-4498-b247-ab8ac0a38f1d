import bcrypt from 'bcryptjs';
import { prisma } from '../../config/db';
import jwt from 'jsonwebtoken';

import { Request, Response } from 'express';

export const register = async (req: Request, res: Response) => {
  const { email, name, password } = req.body;
  const hashedPassword = await bcrypt.hash(password, 10);
  const user = await prisma.user.create({
    data: { email, name, password: hashedPassword }
  });
  res.json({ message: 'User registered', user: { id: user.id, email: user.email, name: user.name } });
};

export const login = async (req: Request, res: Response) => {
    const { email, password } = req.body;
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) return res.status(401).json({ error: 'Invalid credentials' });
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) return res.status(401).json({ error: 'Invalid credentials' });
  
    const token = jwt.sign(
        { userId: user.id },
        process.env.['JWT_SECRET'] || 'your_default_secret',
        { expiresIn: '1d' }
      );
    res.json({ token, user: { id: user.id, email: user.email, name: user.name } });
  };