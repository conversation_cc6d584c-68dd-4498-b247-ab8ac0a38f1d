import bcrypt from 'bcryptjs';
import { prisma } from '../../config/db';
import jwt from 'jsonwebtoken';
import { JWTService } from '../../services/jwt.service';
import { ROLE_PERMISSIONS } from '../../config/permissions';


import { Request, Response } from 'express';

export const register = async (req: Request, res: Response) => {
  try {
    const { email, name, password } = req.body;
    
    // Add validation
    if (!email || !name || !password) {
      return res.status(400).json({ error: 'Email, name, and password are required' });
    }
    
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters' });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      return res.status(409).json({ error: 'User already exists' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await prisma.user.create({
      data: { email, name, password: hashedPassword }
    });
    
    res.status(201).json({ 
      message: 'User registered successfully', 
      user: { id: user.id, email: user.email, name: user.name } 
    });
  } catch (error) {
    res.status(500).json({ error: 'Registration failed' });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Add validation
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Find user by email
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: { id: user.id, email: user.email, name: user.name }
    });
  } catch (error) {
    res.status(500).json({ error: 'Login failed' });
  }
};


export class AuthController {
  private jwtService: JWTService;

  constructor() {
    this.jwtService = new JWTService();
  }

  // Enhanced login with role and profile data
  async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      // Find user with profile relations
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          clientProfile: true,
          staffProfile: true,
          mechanicProfile: true,
        },
      });

      if (!user || !await bcrypt.compare(password, user.password)) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Determine profile type and ID
      let profileId: number | undefined;
      let profileType: string | undefined;

      if (user.clientProfile) {
        profileId = user.clientProfile.id;
        profileType = 'client';
      } else if (user.staffProfile) {
        profileId = user.staffProfile.id;
        profileType = 'staff';
      } else if (user.mechanicProfile) {
        profileId = user.mechanicProfile.id;
        profileType = 'mechanic';
      }

      // Generate token with role information
      const token = this.jwtService.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
        profileId,
        profileType,
      });

      // Get user permissions
      const permissions = ROLE_PERMISSIONS[user.role] || [];

      res.json({
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          profileType,
          permissions,
        },
      });
    } catch (error) {
      res.status(500).json({ error: 'Login failed' });
    }
  }

  // Role-specific registration
  async registerClient(req: Request, res: Response) {
    // Client registration with profile creation
  }

  async registerStaff(req: Request, res: Response) {
    // Staff registration with profile creation
  }

  async registerMechanic(req: Request, res: Response) {
    // Mechanic registration with profile creation
  }
}