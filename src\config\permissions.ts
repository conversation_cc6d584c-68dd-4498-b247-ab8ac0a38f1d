// Permission Constants
export const PERMISSIONS = {
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // Profile Management
  PROFILE_CREATE: 'profile:create',
  PROFILE_READ_OWN: 'profile:read:own',
  PROFILE_READ_ALL: 'profile:read:all',
  PROFILE_UPDATE_OWN: 'profile:update:own',
  PROFILE_UPDATE_ALL: 'profile:update:all',
  
  // Car Management
  CAR_CREATE: 'car:create',
  CAR_READ: 'car:read',
  CAR_UPDATE: 'car:update',
  CAR_DELETE: 'car:delete',
  
  // Rental Management
  RENTAL_CREATE: 'rental:create',
  RENTAL_READ_OWN: 'rental:read:own',
  RENTAL_READ_ALL: 'rental:read:all',
  RENTAL_UPDATE: 'rental:update',
  RENTAL_DELETE: 'rental:delete',
} as const;

// Role-Permission Mapping
export const ROLE_PERMISSIONS: RolePermissions = {
  CLIENT: [
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_UPDATE_OWN,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_READ_OWN,
  ],
  
  RECEPTION: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.PROFILE_READ_ALL,
    PERMISSIONS.PROFILE_UPDATE_ALL,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_UPDATE,
  ],
  
  ADMIN: [
    // All permissions
    ...Object.values(PERMISSIONS),
  ],
  
  MECHANIC: [
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_UPDATE_OWN,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_UPDATE,
  ],
  
  TEAM_LEADER: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.PROFILE_READ_ALL,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_UPDATE,
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_UPDATE,
  ],
  
  ASSISTANCE: [
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.RENTAL_READ_ALL,
  ],
};