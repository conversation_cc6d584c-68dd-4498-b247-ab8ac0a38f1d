import { Role } from '../../generated/prisma';
import { RolePermissions } from '../types/auth';

// Permission Constants - Granular permissions for different actions
export const PERMISSIONS = {
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_LIST: 'user:list',

  // Profile Management
  PROFILE_CREATE: 'profile:create',
  PROFILE_READ_OWN: 'profile:read:own',
  PROFILE_READ_ALL: 'profile:read:all',
  PROFILE_UPDATE_OWN: 'profile:update:own',
  PROFILE_UPDATE_ALL: 'profile:update:all',
  PROFILE_DELETE: 'profile:delete',

  // Car Management
  CAR_CREATE: 'car:create',
  CAR_READ: 'car:read',
  CAR_UPDATE: 'car:update',
  CAR_DELETE: 'car:delete',
  CAR_LIST: 'car:list',

  // Rental Management
  RENTAL_CREATE: 'rental:create',
  RENTAL_READ_OWN: 'rental:read:own',
  RENTAL_READ_ALL: 'rental:read:all',
  RENTAL_UPDATE: 'rental:update',
  RENTAL_DELETE: 'rental:delete',
  RENTAL_LIST: 'rental:list',

  // Driver License Management
  LICENSE_CREATE: 'license:create',
  LICENSE_READ_OWN: 'license:read:own',
  LICENSE_READ_ALL: 'license:read:all',
  LICENSE_UPDATE_OWN: 'license:update:own',
  LICENSE_UPDATE_ALL: 'license:update:all',
  LICENSE_DELETE: 'license:delete',

  // System Administration
  ADMIN_DASHBOARD: 'admin:dashboard',
  ADMIN_SETTINGS: 'admin:settings',
  ADMIN_REPORTS: 'admin:reports',

  // Reception Operations
  RECEPTION_DASHBOARD: 'reception:dashboard',
  RECEPTION_CHECKIN: 'reception:checkin',
  RECEPTION_CHECKOUT: 'reception:checkout',

  // Mechanic Operations
  MECHANIC_DASHBOARD: 'mechanic:dashboard',
  MECHANIC_MAINTENANCE: 'mechanic:maintenance',
  MECHANIC_INSPECTION: 'mechanic:inspection',

  // Team Leader Operations
  TEAM_REPORTS: 'team:reports',
  TEAM_MANAGEMENT: 'team:management',

  // Assistance Operations
  ASSISTANCE_SUPPORT: 'assistance:support',
  ASSISTANCE_EMERGENCY: 'assistance:emergency',
} as const;

// Role-Permission Mapping - Defines what each role can do
export const ROLE_PERMISSIONS: RolePermissions = {
  CLIENT: [
    // Profile management
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_UPDATE_OWN,

    // License management
    PERMISSIONS.LICENSE_CREATE,
    PERMISSIONS.LICENSE_READ_OWN,
    PERMISSIONS.LICENSE_UPDATE_OWN,

    // Car browsing
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_LIST,

    // Rental management
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_READ_OWN,
  ],

  RECEPTION: [
    // User management
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_LIST,

    // Profile management
    PERMISSIONS.PROFILE_READ_ALL,
    PERMISSIONS.PROFILE_UPDATE_ALL,

    // License management
    PERMISSIONS.LICENSE_READ_ALL,
    PERMISSIONS.LICENSE_UPDATE_ALL,

    // Car management
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_LIST,
    PERMISSIONS.CAR_UPDATE,

    // Rental management
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_UPDATE,
    PERMISSIONS.RENTAL_LIST,

    // Reception operations
    PERMISSIONS.RECEPTION_DASHBOARD,
    PERMISSIONS.RECEPTION_CHECKIN,
    PERMISSIONS.RECEPTION_CHECKOUT,
  ],

  ADMIN: [
    // All permissions - Admin has full access
    ...Object.values(PERMISSIONS),
  ],

  MECHANIC: [
    // Profile management
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_UPDATE_OWN,

    // Car management
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_LIST,
    PERMISSIONS.CAR_UPDATE,

    // Rental viewing for maintenance
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_LIST,

    // Mechanic operations
    PERMISSIONS.MECHANIC_DASHBOARD,
    PERMISSIONS.MECHANIC_MAINTENANCE,
    PERMISSIONS.MECHANIC_INSPECTION,
  ],

  TEAM_LEADER: [
    // User management
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_LIST,

    // Profile management
    PERMISSIONS.PROFILE_READ_ALL,
    PERMISSIONS.PROFILE_UPDATE_OWN,

    // Car management
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_LIST,
    PERMISSIONS.CAR_UPDATE,

    // Rental management
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_UPDATE,
    PERMISSIONS.RENTAL_LIST,

    // Team operations
    PERMISSIONS.TEAM_REPORTS,
    PERMISSIONS.TEAM_MANAGEMENT,

    // Reception operations (can cover reception duties)
    PERMISSIONS.RECEPTION_DASHBOARD,
    PERMISSIONS.RECEPTION_CHECKIN,
    PERMISSIONS.RECEPTION_CHECKOUT,
  ],

  ASSISTANCE: [
    // Profile management
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_UPDATE_OWN,

    // Car viewing
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_LIST,

    // Rental viewing for assistance
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_LIST,

    // User viewing for assistance
    PERMISSIONS.USER_READ,
    PERMISSIONS.PROFILE_READ_ALL,

    // Assistance operations
    PERMISSIONS.ASSISTANCE_SUPPORT,
    PERMISSIONS.ASSISTANCE_EMERGENCY,
  ],
};

// Helper function to get permissions for a role
export const getPermissionsForRole = (role: Role): string[] => {
  return ROLE_PERMISSIONS[role] || [];
};

// Helper function to check if a role has a specific permission
export const hasPermission = (role: Role, permission: string): boolean => {
  const permissions = getPermissionsForRole(role);
  return permissions.includes(permission);
};

// Helper function to check if a role has any of the specified permissions
export const hasAnyPermission = (role: Role, permissions: string[]): boolean => {
  const userPermissions = getPermissionsForRole(role);
  return permissions.some(permission => userPermissions.includes(permission));
};

// Helper function to check if a role has all of the specified permissions
export const hasAllPermissions = (role: Role, permissions: string[]): boolean => {
  const userPermissions = getPermissionsForRole(role);
  return permissions.every(permission => userPermissions.includes(permission));
};

// Permission groups for easier management
export const PERMISSION_GROUPS = {
  USER_MANAGEMENT: [
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_LIST,
  ],
  PROFILE_MANAGEMENT: [
    PERMISSIONS.PROFILE_CREATE,
    PERMISSIONS.PROFILE_READ_OWN,
    PERMISSIONS.PROFILE_READ_ALL,
    PERMISSIONS.PROFILE_UPDATE_OWN,
    PERMISSIONS.PROFILE_UPDATE_ALL,
    PERMISSIONS.PROFILE_DELETE,
  ],
  CAR_MANAGEMENT: [
    PERMISSIONS.CAR_CREATE,
    PERMISSIONS.CAR_READ,
    PERMISSIONS.CAR_UPDATE,
    PERMISSIONS.CAR_DELETE,
    PERMISSIONS.CAR_LIST,
  ],
  RENTAL_MANAGEMENT: [
    PERMISSIONS.RENTAL_CREATE,
    PERMISSIONS.RENTAL_READ_OWN,
    PERMISSIONS.RENTAL_READ_ALL,
    PERMISSIONS.RENTAL_UPDATE,
    PERMISSIONS.RENTAL_DELETE,
    PERMISSIONS.RENTAL_LIST,
  ],
} as const;