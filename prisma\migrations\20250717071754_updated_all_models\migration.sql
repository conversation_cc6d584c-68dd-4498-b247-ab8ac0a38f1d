/*
  Warnings:

  - The values [USER,STAFF] on the enum `Role` will be removed. If these variants are still used in the database, this will fail.
  - The primary key for the `DriverLicense` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdAt` on the `DriverLicense` table. All the data in the column will be lost.
  - You are about to drop the column `licenseNumber` on the `DriverLicense` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `DriverLicense` table. All the data in the column will be lost.
  - You are about to drop the column `userProfileId` on the `DriverLicense` table. All the data in the column will be lost.
  - The `id` column on the `DriverLicense` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `EmergencyContact` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserProfile` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[dlNumber]` on the table `DriverLicense` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `dlNumber` to the `DriverLicense` table without a default value. This is not possible if the table is not empty.
  - Added the required column `profileId` to the `DriverLicense` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "Role_new" AS ENUM ('CLIENT', 'ADMIN', 'RECEPTION', 'MECHANIC', 'TEAM_LEADER', 'ASSISTANCE');
ALTER TABLE "User" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TYPE "Role" RENAME TO "Role_old";
ALTER TYPE "Role_new" RENAME TO "Role";
DROP TYPE "Role_old";
ALTER TABLE "User" ALTER COLUMN "role" SET DEFAULT 'CLIENT';
COMMIT;

-- DropForeignKey
ALTER TABLE "DriverLicense" DROP CONSTRAINT "DriverLicense_userProfileId_fkey";

-- DropForeignKey
ALTER TABLE "EmergencyContact" DROP CONSTRAINT "EmergencyContact_userProfileId_fkey";

-- DropForeignKey
ALTER TABLE "UserProfile" DROP CONSTRAINT "UserProfile_userId_fkey";

-- DropIndex
DROP INDEX "DriverLicense_licenseNumber_key";

-- DropIndex
DROP INDEX "DriverLicense_userProfileId_key";

-- AlterTable
ALTER TABLE "DriverLicense" DROP CONSTRAINT "DriverLicense_pkey",
DROP COLUMN "createdAt",
DROP COLUMN "licenseNumber",
DROP COLUMN "updatedAt",
DROP COLUMN "userProfileId",
ADD COLUMN     "backViewUrl" TEXT,
ADD COLUMN     "conditions" TEXT,
ADD COLUMN     "dlNumber" TEXT NOT NULL,
ADD COLUMN     "frontViewUrl" TEXT,
ADD COLUMN     "profileId" INTEGER NOT NULL,
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ADD CONSTRAINT "DriverLicense_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "User" DROP CONSTRAINT "User_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "role" SET DEFAULT 'CLIENT',
ADD CONSTRAINT "User_pkey" PRIMARY KEY ("id");

-- DropTable
DROP TABLE "EmergencyContact";

-- DropTable
DROP TABLE "UserProfile";

-- CreateTable
CREATE TABLE "ClientProfile" (
    "id" SERIAL NOT NULL,
    "customerId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "postCode" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "birthday" TIMESTAMP(3) NOT NULL,
    "isCorporate" BOOLEAN NOT NULL DEFAULT false,
    "companyName" TEXT,
    "emergencyContactPersonName" TEXT,
    "emergencyContactRelationship" TEXT,
    "emergencyContactPhoneNumber" TEXT,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "ClientProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StaffProfile" (
    "id" SERIAL NOT NULL,
    "employeeId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "position" TEXT,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "StaffProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MechanicProfile" (
    "id" SERIAL NOT NULL,
    "employeeId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "certificationNumber" TEXT NOT NULL,
    "specialization" TEXT,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "MechanicProfile_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ClientProfile_customerId_key" ON "ClientProfile"("customerId");

-- CreateIndex
CREATE UNIQUE INDEX "ClientProfile_phoneNumber_key" ON "ClientProfile"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "ClientProfile_userId_key" ON "ClientProfile"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "StaffProfile_employeeId_key" ON "StaffProfile"("employeeId");

-- CreateIndex
CREATE UNIQUE INDEX "StaffProfile_phoneNumber_key" ON "StaffProfile"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "StaffProfile_userId_key" ON "StaffProfile"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "MechanicProfile_employeeId_key" ON "MechanicProfile"("employeeId");

-- CreateIndex
CREATE UNIQUE INDEX "MechanicProfile_phoneNumber_key" ON "MechanicProfile"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "MechanicProfile_userId_key" ON "MechanicProfile"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "DriverLicense_dlNumber_key" ON "DriverLicense"("dlNumber");

-- AddForeignKey
ALTER TABLE "ClientProfile" ADD CONSTRAINT "ClientProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StaffProfile" ADD CONSTRAINT "StaffProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MechanicProfile" ADD CONSTRAINT "MechanicProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DriverLicense" ADD CONSTRAINT "DriverLicense_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "ClientProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
